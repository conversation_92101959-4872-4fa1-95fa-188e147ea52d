import {
    _decorator,
    Component,
    director,
    instantiate,
    math,
    Node,
    Prefab,
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>er,
    TiledMap,
    tween,
    UITransform,
    Vec3,
    assetManager,
} from 'cc';
import { EventBus } from '../EventBus';
import { GameConfig } from '../GameConfig';
import { GameData } from '../GameData';
import { Item } from '../Item';
import { ExitDoor } from './ExitDoor';
import { LevelGenerator } from '../LevelGenerator';
import { Mine } from '../Mine';
import { Player } from '../Player';
import { Smoke } from '../Smoke';
import { UserData } from '../Utils/UserData';
import { AdMobRewardedVideo } from '../AdMob/AdMobRewardedVideo';
import { WECHAT } from 'cc/env';
import { Saw } from '../Saw';
import { RoundBlade } from '../RoundBlade';
import { TwinBlade } from '../TwinBlade';
import { Chapters } from '../Chapters/Chapters';
import { CommonAssetsCtl } from '../v1/Common/CommonAssetsCtl';
import { saveUserData } from '../Utils/Tools';
import { FeverChapters } from '../FeverChapters/FeverChapters';
import { sys } from 'cc';
import { game } from 'cc';
import { Chapters01 } from '../Chapters/Chapter01';
import { BackgroundVertical } from '../v1/Common/BackgroundVertical';
import { MapUtils } from '../Utils/MapUtils';
import { TreasureBox } from './TreasureBox';
import { HiddenRoomStateManager } from '../Utils/HiddenRoomStateManager';
import { HiddenRoomMask } from './HiddenRoomMask';
const { ccclass, property } = _decorator;

@ccclass('HiddenMap')
export class HiddenMap extends Component {
    @property(Prefab)
    playerFab: Prefab;
    @property(Prefab)
    scarFab: Prefab;
    @property(SpriteFrame)
    nextMinePortalFrame: SpriteFrame;
    @property(Prefab)
    playerBuffPrefab: Prefab; // 玩家buff

    @property(Prefab)
    public mine01Fab: Prefab;

    @property(Prefab)
    public trap01Fab: Prefab;

    @property(Prefab)
    public bombFab: Prefab;
    @property(Prefab)
    public bombFlyingFab: Prefab;
    @property(Prefab)
    public bombGrenadeFab: Prefab;

    @property(Prefab)
    rockFallFab: Prefab;
    @property(Prefab)
    rockFall2Fab: Prefab;
    @property(Prefab)
    smokeFab: Prefab;
    @property(Prefab)
    glowingTileFab: Prefab;

    @property(Prefab)
    oneCardFab: Prefab;

    @property(Prefab)
    startBtnFab: Prefab;
    @property(SpriteFrame)
    dirtFrame: SpriteFrame;
    @property(SpriteFrame)
    adFrame: SpriteFrame;
    @property(Prefab)
    mineHelpFab: Prefab;
    @property(Prefab)
    sawFab: Prefab;
    @property(Prefab)
    roundBladeFab: Prefab;
    @property(Prefab)
    twinBladeFab: Prefab;

    @property(Prefab)
    itemFloatUpPrefab: Prefab;
    @property(Prefab)
    playerHurtPrafab: Prefab;

    @property(Prefab)
    mapBlockNodePrefab: Prefab;

    @property(Prefab)
    treasureBox: Prefab;

    @property(Prefab)
    hiddenRoomMaskFab: Prefab;

    @property(SpriteFrame)
    diamond: SpriteFrame;
    @property(SpriteFrame)
    coin: SpriteFrame;

    private map: TiledMap;
    public mapSize: Size;
    public tileSize: Size;
    private mainCamera: Node;
    private uiCamera: Node;
    private rootNode: Node;
    public player: Node;
    public groundLayer: TiledLayer;
    public itemsLayer: TiledLayer;

    public tileGIDMaxY = GameConfig.mapMaxLevel - 2; // 不包含
    public mapBlocks = []; // 生成地图
    // public mineBlocks = []; // []，Node, 矿石
    public blocks = []; // Node, 矿石，敌人(目前没有)等等
    // GIDBlocks eg. [{kind: MINE_A19, initialHP: xx, subKind: xx, state: GameConfig.mineState.IN_DIRT, gid: vec3, isCreated: false}...]
    // public GIDBlocks = []; // 整张地图全部经验石、矿石、问号、陷阱（石头，地刺）
    // public nodeBlocks = []; // Node， GIDBlocks
    public trapRocks = []; // Node， 再存一份石头的数据，用于提升性能，因此这个性能更好
    // GIDTrapRocks eg. [{kind: TRAP_ROCK, initialHP: xx, subKind: xx, state: GameConfig.mineState.IN_DIRT, gid: vec3, isCreated: false}...]
    // public nodeTrapRocks = []; // Node， 再存一份石头的数据，用于提升性能，因此这个性能更好
    public scars = []; // Node, 裂痕，只有没有blocks的地方有
    private exitGIDs = []; // gid,  vec3
    public glowingTiles = []; // Node, 隐藏的会发光的道具的坐标，在土里面，挖出后显示，这个上面不会有blocks
    // GIDGlowingTiles eg.[gid, gid, gid...]
    // public GIDGlowingTiles = []; // 整张地图全部隐藏的会发光的道具的坐标，在土里面，挖出后显示，这个上面不会有blocks

    private mainCameraState = GameConfig.cameraStates.FOLLOWING;

    public targetDepth = -1; // 不等于-1表示本关目标是层数不是矿石，只能二选一，但是可以是多个矿石目标
    public treasureGID: Vec3 = new Vec3(); // 宝箱的gid
    public treasureNode: Node = null;
    public commonAssets: CommonAssetsCtl;
    private uiNode: Node = null; // UI节点，用于宝箱奖励飞行动画

    // 当前章节配置
    private currentChapterConfig: any = null;
    private chapIndex: number = 0;

    public adService: AdMobRewardedVideo = null;
    private bgScript: BackgroundVertical;
    private timePassed = 0; // 防止重复生成地图的标志
    private isMapGenerated = false;
    private isCaveFloorGenerated = false;

    // 防止宝箱状态重复保存的标志
    private treasureStateAlreadySaved = false;

    // 防止退出动画重复触发的标志
    private isExitingRoom = false;

    // 隐藏房间洞穴区域配置
    private hiddenRoomCaveConfig = {
        startX: 5, // 洞穴起始X坐标
        endX: 20, // 洞穴结束X坐标 (15个tile长度)
        startY: 6, // 洞穴起始Y坐标
        endY: 8, // 洞穴结束Y坐标 (3个tile高度)
        treasureX: 19, // 宝箱X坐标
        treasureY: 8, // 宝箱Y坐标 - 站在地面上
    };

    /**
     * 检查指定坐标是否在隐藏房间洞穴区域内
     */
    private isInHiddenRoomCave(x: number, y: number): boolean {
        return (
            x >= this.hiddenRoomCaveConfig.startX &&
            x <= this.hiddenRoomCaveConfig.endX &&
            y >= this.hiddenRoomCaveConfig.startY &&
            y <= this.hiddenRoomCaveConfig.endY
        );
    }

    /**
     * 检查指定坐标是否是洞穴边界（需要生成墙壁）
     */
    private isHiddenRoomCaveBorder(x: number, y: number): boolean {
        // 下边界 - 只检查洞穴下方的边界
        if (
            y === this.hiddenRoomCaveConfig.endY + 1 &&
            x >= this.hiddenRoomCaveConfig.startX &&
            x <= this.hiddenRoomCaveConfig.endX
        ) {
            return true;
        }
        // 左右边界
        if (
            (x === this.hiddenRoomCaveConfig.startX - 1 ||
                x === this.hiddenRoomCaveConfig.endX + 1) &&
            y >= this.hiddenRoomCaveConfig.startY &&
            y <= this.hiddenRoomCaveConfig.endY
        ) {
            return true;
        }
        return false;
    }

    protected onLoad(): void {
        GameConfig.currentGameState = null;
        GameData.mode = UserData.nextMode;
        UserData.nextMode = 'general';
        saveUserData();

        game.frameRate = 60;
    }

    start() {
        // 监听退出隐藏房间事件
        EventBus.on('exitHiddenRoom', this.handleExitHiddenRoom, this);

        this.commonAssets = director
            .getScene()
            .getChildByPath('Canvas/CommonAssets')
            .getComponent(CommonAssetsCtl);

        if (!this.commonAssets) {
            console.error('CommonAssets 组件未找到!');
        }

        EventBus.emit('playSound', 'play2');
        let res = JSON.parse(
            JSON.stringify(Chapters[UserData.currentChapter - 1][UserData.currentLevel - 1])
        ); // clone
        if (GameData.mode === 'fever') {
            res = JSON.parse(JSON.stringify(FeverChapters[UserData.currentChapter - 1])); // clone
        }
        if (!GameData.levelGenerator) GameData.levelGenerator = new LevelGenerator();
        GameData.currentLevelConfig = GameData.levelGenerator.generateLevel({
            luck: 1, // 玩家等级
            res: res,
            totalLevels: this.tileGIDMaxY,
        });
        if (GameData.mode === 'general') {
            GameData.currentLevelTargetData = GameData.currentLevelConfig.targets.map(
                (item: any) => ({
                    ...item,
                    currentCount: 0,
                })
            );
            let depthConfig = GameData.currentLevelTargetData.find((v: any) => v.kind == 'depth');
            if (depthConfig) {
                this.targetDepth = depthConfig.count;
            }
        } else {
            this.targetDepth = GameConfig.mapMaxLevel;
        }

        // 添加卡片到卡片池 并去重
        GameData.cardsPool = [...new Set([...GameData.cardsPool, ...UserData.cards])];

        // this.winSize = view.getVisibleSize();

        this.mainCamera = director.getScene().getChildByPath('Canvas/MainCamera');
        this.uiCamera = director.getScene().getChildByPath('Canvas/UICamera');
        this.rootNode = director.getScene().getChildByPath('Canvas');
        this.uiNode = director.getScene().getChildByPath('Canvas/UI'); // 获取UI节点

        // let gs = this.map.getObjectGroups();
        this.mapSize = new Size(25, 15); // 隐藏房间使用适中的地图尺寸
        this.tileSize = new Size(64, 64); // 每个格子64*64
        // this.mapWidth = this.mapSize.width * this.tileSize.width;
        // this.mapHeight = this.mapSize.height * this.tileSize.height;
        // this.mapPlayingTilesSize = this.mapSize.width - 2 * 2; // 左右两边2格

        // 地图目前放大了1.2倍(在TiledMap节点上配置)，所以这里定位时要考虑到缩放
        // 隐藏房间的地图位置需要居中显示
        this.node.setPosition(new Vec3(0, 0, 0));

        // bg color
        // let totalHeight = this.winSize.height + this.mapHeight;
        this.bgScript = director
            .getScene()
            .getChildByPath('Canvas/Bg2')
            .getComponent(BackgroundVertical);

        // 获取当前章节配置
        this.getCurrentChapterConfig();

        // 生成隐藏房间洞穴地图
        let playerGID = new Vec3(
            this.hiddenRoomCaveConfig.startX + 1,
            this.hiddenRoomCaveConfig.endY,
            0
        ); // 玩家站在洞穴地面左侧
        this.SpawnRoguelikeMaps();

        // 注释掉立即保存访问状态的逻辑，改为在房间道具生成完成后保存
        // 这样可以确保首次进入时能正常生成房间奖励

        // 生成玩家
        try {
            this.player = instantiate(this.playerFab);
            this.player.getComponent(UITransform).priority = GameConfig.UIPriority.player;
            this.player.setSiblingIndex(GameConfig.UIPriority.player);
            let playerPos = MapUtils.getLocationByGID(playerGID);
            this.player.setPosition(playerPos);
            this.node.addChild(this.player);

            // 设置摄像机初始位置 - 在隐藏房间中直接设置到玩家位置
            let initialCameraPos = playerPos.clone();
            this.mainCamera.setPosition(initialCameraPos);

            // 播放玩家出现在隐藏房间的动画
            this.createAppearInHiddenRoomAnimation();

            // 记录相机目标位置用于跟随
            // this.cameraTargetPos = playerPos.clone();
        } catch (error) {
            console.error('生成玩家时出错:', error);
        }

        if (GameData.mode === 'general') {
            EventBus.emit('loadGameMask');
        }

        // 在玩家位置附近创建出口
        this.createExitNearPlayer();

        if (WECHAT) {
        } else {
            this.adService = AdMobRewardedVideo.getInstance();
            this.adService.preloadAd();
        }

        // 监听宝箱打开事件
        EventBus.on('treasureOpened', this.onTreasureOpened, this);
    }

    onDestroy() {
        // 重置退出状态
        this.isExitingRoom = false;

        EventBus.off('treasureOpened', this.onTreasureOpened, this);
        EventBus.off('exitHiddenRoom', this.handleExitHiddenRoom, this); // 移除退出事件监听
        // 销毁矿石节点
        this.blocks.forEach(block => block.destroy());
        this.blocks = [];

        // 销毁陷阱节点
        this.trapRocks.forEach(trap => trap.destroy());
        this.trapRocks = [];

        // 销毁发光道具节点
        this.glowingTiles.forEach(tile => tile.destroy());
        this.glowingTiles = [];

        // 销毁裂痕节点
        this.scars.forEach(scar => scar.destroy());
        this.scars = [];

        // 销毁宝箱节点
        if (this.treasureNode) {
            this.treasureNode.destroy();
            this.treasureNode = null;
        }

        // 释放地图资源
        if (this.map && this.map.tmxAsset) {
            assetManager.releaseAsset(this.map.tmxAsset);
            this.map.tmxAsset = null;
        }
    }

    public SpawnRoguelikeMaps() {
        // 防止重复生成地图 - 特别是被 Player.update() 重复调用
        if (this.isMapGenerated) {
            return;
        }
        this.isMapGenerated = true;

        // 隐藏房间使用简化的生成范围
        let minimalX = 1;
        let maxX = this.mapSize.width - 2; // 23
        let minimalY = 1;
        let maxY = this.mapSize.height - 2; // 13

        let setupTile = (b: Node, tileFrame: string) => {
            try {
                if (this.commonAssets && this.commonAssets[tileFrame]) {
                    b.getComponent(Sprite).spriteFrame = this.commonAssets[tileFrame];
                    b.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 2;
                    this.node.addChild(b);
                    this.mapBlocks.push(b);
                } else {
                    console.warn(`缺少瓦片资源: ${tileFrame}`);
                    // 使用默认瓦片
                    b.getComponent(Sprite).spriteFrame =
                        this.commonAssets['chapter1MapMiddleCenterFrame'];
                    b.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 2;
                    this.node.addChild(b);
                    this.mapBlocks.push(b);
                }
            } catch (error) {
                console.error('设置瓦片时出错:', error);
                b.destroy();
            }
        };

        let tilesGenerated = 0; // 生成地图块
        for (let y = minimalY; y <= maxY; y++) {
            for (let x = minimalX; x <= maxX; x++) {
                // 检查是否在洞穴区域内
                if (this.isInHiddenRoomCave(x, y)) {
                    // 洞穴内部不生成土块，保持空白
                    continue;
                }

                // 地图上半部分只生成一层石头（Y坐标小于等于洞穴起始Y坐标的区域）
                if (y <= this.hiddenRoomCaveConfig.startY - 1) {
                    // 只在洞穴上方的第一层生成trapRockFrame（即y = startY - 1 = 5）
                    if (y !== this.hiddenRoomCaveConfig.startY - 1) {
                        continue;
                    }

                    // 创建地图块
                    let b = instantiate(this.mapBlockNodePrefab);
                    let bPos = MapUtils.getLocationByGID(new Vec3(x, y, 0));
                    b.setPosition(bPos);

                    // 生成石头层
                    let trapRockFrameName =
                        this.currentChapterConfig?.trapRockFrame || 'trapDwarfRockFrame';

                    if (this.commonAssets && this.commonAssets[trapRockFrameName]) {
                        b.getComponent(Sprite).spriteFrame = this.commonAssets[trapRockFrameName];
                        b.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 2;
                        this.node.addChild(b);
                        this.mapBlocks.push(b);
                    } else {
                        console.warn(`缺少trapRockFrame资源: ${trapRockFrameName}，使用默认瓦片`);
                        b.getComponent(Sprite).spriteFrame =
                            this.commonAssets['chapter1MapMiddleCenterFrame'];
                        b.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 2;
                        this.node.addChild(b);
                        this.mapBlocks.push(b);
                    }

                    tilesGenerated++;
                    // 石头层生成完成，跳过后续的瓦片选择逻辑
                    continue;
                }

                // 地图下半部分只生成三层（洞穴下方限制层数）
                if (y > this.hiddenRoomCaveConfig.endY) {
                    // 只在洞穴下方的三层生成土块（y = endY + 1, endY + 2, endY + 3）
                    if (y > this.hiddenRoomCaveConfig.endY + 3) {
                        continue;
                    }
                }

                // 检查是否是洞穴边界
                let isBorder = this.isHiddenRoomCaveBorder(x, y);

                // 创建地图块
                try {
                    let b = instantiate(this.mapBlockNodePrefab);
                    let bPos = MapUtils.getLocationByGID(new Vec3(x, y, 0));
                    b.setPosition(bPos);

                    // 使用动态章节配置的瓦片选择逻辑
                    if (x === minimalX || x === maxX) {
                        // 左右边界墙壁
                        if (x === minimalX) {
                            setupTile(b, `chapter${this.chapIndex + 1}MapMiddleCenterFrame`);
                        } else {
                            setupTile(b, `chapter${this.chapIndex + 1}MapMiddleCenterFrame`);
                        }
                    } else if (isBorder) {
                        // 洞穴边界 - 根据位置使用不同的墙壁样式
                        if (x === this.hiddenRoomCaveConfig.startX - 1) {
                            // 洞穴左边界
                            setupTile(b, `chapter${this.chapIndex + 1}MapMiddleRightFrame`);
                        } else if (x === this.hiddenRoomCaveConfig.endX + 1) {
                            // 洞穴右边界
                            setupTile(b, `chapter${this.chapIndex + 1}MapMiddleLeftFrame`);
                        } else {
                            // 洞穴上下边界使用中间样式
                            setupTile(b, `chapter${this.chapIndex + 1}MapMiddleCenterFrame`);
                        }
                    } else {
                        // 普通土块
                        setupTile(b, `chapter${this.chapIndex + 1}MapMiddleCenterFrame`);
                    }

                    tilesGenerated++;
                } catch (error) {
                    console.error(`生成地图块时出错 (${x}, ${y}):`, error);
                }
            }
        }

        // 生成洞穴地面 - 在洞穴底部添加地面瓦片
        this.generateCaveFloor();

        // 在宝箱位置生成宝箱
        this.spawnTreasureBox();

        // 生成房间道具
        this.spawnRoomItems();
    }

    /**
     * 生成洞穴地面 - 使用第一层的样式
     */
    private generateCaveFloor() {
        // 防止重复生成洞穴地面
        if (this.isCaveFloorGenerated) {
            return;
        }

        try {
            this.isCaveFloorGenerated = true;

            // 在洞穴底部生成地面
            let floorY = this.hiddenRoomCaveConfig.endY + 1;

            for (
                let x = this.hiddenRoomCaveConfig.startX;
                x <= this.hiddenRoomCaveConfig.endX;
                x++
            ) {
                // 先生成地图块（视觉效果）
                let b = instantiate(this.mapBlockNodePrefab);
                let bPos = MapUtils.getLocationByGID(new Vec3(x, floorY, 0));
                b.setPosition(bPos);

                // 使用动态章节配置的底部样式作为地面
                let tileFrame: string;
                if (x === this.hiddenRoomCaveConfig.startX) {
                    tileFrame = `chapter${this.chapIndex + 1}MapBottomLeftFrame`;
                } else if (x === this.hiddenRoomCaveConfig.endX) {
                    tileFrame = `chapter${this.chapIndex + 1}MapBottomRightFrame`;
                } else {
                    tileFrame = `chapter${this.chapIndex + 1}MapBottomCenterFrame`;
                }

                if (this.commonAssets && this.commonAssets[tileFrame]) {
                    b.getComponent(Sprite).spriteFrame = this.commonAssets[tileFrame];
                    b.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 2;
                    this.node.addChild(b);
                    this.mapBlocks.push(b);
                } else {
                    console.warn(`缺少地面瓦片资源: ${tileFrame}`);
                    b.destroy();
                    continue; // 如果地图块创建失败，跳过不可破坏石头的创建
                }

                // 在地面位置生成不可破坏的石头（功能性）
                // 这样地面既有视觉效果，又不可被破坏
                this.spawnUnbreakableRockAtGID(new Vec3(x, floorY, 0));
            }
        } catch (error) {
            console.error('生成洞穴地面时出错:', error);
        }
    }

    /**
     * 获取当前章节配置
     */
    private getCurrentChapterConfig() {
        // 根据当前关卡获取章节配置
        let currentLevel = UserData.currentLevel || 1;

        for (let i = 0; i < Chapters01.length; i++) {
            if (
                currentLevel >= Chapters01[i]['levels'][0] &&
                currentLevel <= Chapters01[i]['levels'][1]
            ) {
                this.chapIndex = i;
                this.currentChapterConfig = Chapters01[i];
                return;
            }
        }

        // 如果没有找到配置，使用第一个章节配置
        this.chapIndex = 0;
        this.currentChapterConfig = Chapters01[0];
        console.warn('未找到匹配的章节配置，使用默认配置');
    }

    /**
     * 生成房间道具
     */
    private spawnRoomItems() {
        if (
            !this.currentChapterConfig ||
            !this.currentChapterConfig.hiddenRooms ||
            !this.currentChapterConfig.hiddenRooms.roomItems
        ) {
            console.warn('没有找到房间道具配置');
            return;
        }

        // 检查隐藏房间是否已经进入过，如果进入过就不生成房间奖励
        // 使用 this.currentChapterConfig 而不是 GameData.currentLevelConfig 确保配置一致性
        let entranceGIDObj = null;

        if (
            this.currentChapterConfig &&
            this.currentChapterConfig.hiddenRooms &&
            this.currentChapterConfig.hiddenRooms.entranceGID
        ) {
            const entranceGID = this.currentChapterConfig.hiddenRooms.entranceGID;
            entranceGIDObj = { x: entranceGID[0], y: entranceGID[1] };
            const hasEntered = HiddenRoomStateManager.isEntered(entranceGIDObj);

            if (hasEntered) {
                return;
            }
        } else {
            return;
        }

        try {
            const roomItems = this.currentChapterConfig.hiddenRooms.roomItems;

            // 在洞穴内随机位置生成道具
            for (let i = 0; i < roomItems.length; i++) {
                const item = roomItems[i];

                // 随机选择洞穴内的位置
                let randomX =
                    this.hiddenRoomCaveConfig.startX +
                    1 +
                    Math.floor(
                        Math.random() *
                            (this.hiddenRoomCaveConfig.endX - this.hiddenRoomCaveConfig.startX - 2)
                    );
                let randomY =
                    this.hiddenRoomCaveConfig.startY +
                    Math.floor(
                        Math.random() *
                            (this.hiddenRoomCaveConfig.endY - this.hiddenRoomCaveConfig.startY + 1)
                    );

                // 避免与宝箱位置重叠
                if (
                    randomX === this.hiddenRoomCaveConfig.treasureX &&
                    randomY === this.hiddenRoomCaveConfig.treasureY
                ) {
                    randomX =
                        randomX > this.hiddenRoomCaveConfig.startX + 1 ? randomX - 1 : randomX + 1;
                }

                this.spawnRoomItem(item, new Vec3(randomX, randomY, 0));
            }

            // 道具生成完成后，保存进入状态，避免重复生成道具
            if (entranceGIDObj) {
                HiddenRoomStateManager.saveEntered(entranceGIDObj);
            }
        } catch (error) {
            console.error('[HIDDEN_ROOM] ❌ 生成房间道具时出错:', error);
            console.error('[HIDDEN_ROOM] 错误堆栈:', error.stack);
        }
    }

    /**
     * 在指定位置生成单个房间道具（支持更多自定义参数）
     */
    private spawnRoomItem(itemConfig: any, gid: Vec3) {
        try {
            const itemPos = MapUtils.getLocationByGID(gid);

            // 类型到生成逻辑的映射
            const typeMap = {
                coin: () => {
                    let [item, ms] = this.placeItemAt(
                        itemPos,
                        GameConfig.blockKinds.ITEM_HIDDEN_MONEY,
                        null
                    );
                    item.setPosition(item.position.add(new Vec3(0, -8, 0)));
                    item.getComponent(Sprite).enabled = true;
                    ms.beDamaged(999);
                },
                diamond: () => {
                    let [item, ms] = this.placeItemAt(
                        itemPos,
                        GameConfig.blockKinds.ITEM_DIAMOND,
                        null
                    );
                    item.setPosition(item.position.add(new Vec3(0, -8, 0)));
                    item.getComponent(Sprite).enabled = true;
                    ms.beDamaged(999);
                },
                ore: () => {
                    let oreId = itemConfig.id
                        ? parseInt(itemConfig.id)
                        : GameConfig.blockKinds.MINE_A20;
                    let [item, ms] = this.placeItemAt(itemPos, oreId, null);
                    item.setPosition(item.position.add(new Vec3(0, -8, 0)));
                    item.getComponent(Sprite).enabled = true;
                    ms.beDamaged(999);
                },
                key: () => {
                    let [item, ms] = this.placeItemAt(
                        itemPos,
                        GameConfig.blockKinds.ITEM_KEY,
                        null
                    );
                    item.setPosition(item.position.add(new Vec3(0, -8, 0)));
                    item.getComponent(Sprite).enabled = true;
                    ms.beDamaged(999);
                },
                // 可扩展更多类型
            };

            if (typeMap[itemConfig.type]) {
                typeMap[itemConfig.type]();
            } else {
                console.warn(`[HIDDEN_ROOM] ❌ 未知的道具类型: ${itemConfig.type}`);
            }
        } catch (error) {
            console.error(`[HIDDEN_ROOM] ❌ 生成房间道具时出错 (${itemConfig.type}):`, error);
            console.error(`[HIDDEN_ROOM] 错误堆栈:`, error.stack);
        }
    }

    /**
     * 配置宝箱内容
     */
    private configureTreasureBoxContents(treasureBoxComponent: TreasureBox) {
        if (
            !this.currentChapterConfig ||
            !this.currentChapterConfig.hiddenRooms ||
            !this.currentChapterConfig.hiddenRooms.treasureChests
        ) {
            console.warn('没有找到宝箱配置，使用默认内容');
            return;
        }

        try {
            const treasureChests = this.currentChapterConfig.hiddenRooms.treasureChests;

            // 为宝箱设置奖励内容
            if (treasureBoxComponent) {
                treasureBoxComponent.setTreasureContents(treasureChests);
            }
        } catch (error) {
            console.error('配置宝箱内容时出错:', error);
        }
    }

    private spawnTreasureBox() {
        // 防止重复生成宝箱
        if (this.treasureNode) {
            return;
        }

        try {
            // 确保有宝箱Prefab
            if (!this.treasureBox) {
                console.error('没有设置宝箱Prefab(treasureBox)');
                return;
            }

            let treasurePos = MapUtils.getLocationByGID(
                new Vec3(
                    this.hiddenRoomCaveConfig.treasureX,
                    this.hiddenRoomCaveConfig.treasureY,
                    0
                )
            ); // 使用treasureBox Prefab创建宝箱
            let treasure = instantiate(this.treasureBox);

            // 确保宝箱有TreasureBox组件
            let treasureBoxComponent = treasure.getComponent(TreasureBox);
            if (!treasureBoxComponent) {
                treasureBoxComponent = treasure.addComponent(TreasureBox);
            }

            // 根据章节配置设置宝箱内容
            this.configureTreasureBoxContents(treasureBoxComponent);

            // 宝箱状态检查和恢复现在由TreasureBox自己在start方法中处理

            // 设置宝箱位置和UI优先级
            treasure.setPosition(treasurePos);
            treasure.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 1;
            treasure.setSiblingIndex(GameConfig.UIPriority.allUI - 1);

            this.node.addChild(treasure);
            this.treasureNode = treasure;

            // 记录宝箱GID
            this.treasureGID = new Vec3(
                this.hiddenRoomCaveConfig.treasureX,
                this.hiddenRoomCaveConfig.treasureY,
                0
            );
        } catch (error) {
            console.error('生成宝箱时出错:', error);
        }
    }

    public saveLevelMemo(key: string, v: any) {
        let existValue = UserData.levelMemo[key] || {};
        for (let k2 in v) {
            existValue[k2] = v[k2];
        }
        UserData.levelMemo[key] = existValue;
        sys.localStorage.setItem('userData', JSON.stringify(UserData));
    }

    // 保存隐藏房间访问状态 - 访问状态已在 Player.enterHiddenRoom 中统一保存
    public saveHiddenRoomVisited() {
        // 状态已在进入时保存，此方法保留为兼容性接口
        console.log('[HIDDEN_ROOM] 访问状态已在进入时保存，无需重复操作');
    }

    // 检查隐藏房间是否已访问过 - 使用 GID 方案
    public isHiddenRoomVisited(): boolean {
        const currentChapterConfig = GameData.currentLevelConfig;
        if (
            currentChapterConfig &&
            currentChapterConfig.hiddenRooms &&
            currentChapterConfig.hiddenRooms.entranceGID
        ) {
            const entranceGID = currentChapterConfig.hiddenRooms.entranceGID;
            const entranceGIDObj = { x: entranceGID[0], y: entranceGID[1] };
            return HiddenRoomStateManager.isEntered(entranceGIDObj);
        } else {
            console.error('[HIDDEN_ROOM] 无法获取入口GID，无法检查访问状态');
            return false;
        }
    }

    public removeItemLayerTileAt(gid: Vec3, autoUpdate: boolean = true) {
        this.itemsLayer.setTileGIDAt(-1, gid.x, gid.y, 1);
        if (autoUpdate) {
            this.itemsLayer.markForUpdateRenderData(true); // 必须手工调用，否则地图不会更新
        }
    }

    public removeTileAt(gid: Vec3) {
        for (let i = 0; i < this.mapBlocks.length; i++) {
            let b = this.mapBlocks[i];
            let bGID = MapUtils.getGIDByLocation(b.position);
            if (bGID.x === gid.x && bGID.y === gid.y) {
                b.destroy();
                this.mapBlocks.splice(i, 1);
                break;
            }
        }
    }

    public placeItemAt(pos: Vec3, subKind: number, chapConfig: any): [Node, Mine] {
        if (!chapConfig) {
            [chapConfig] = this.getChapConfigByGID(MapUtils.getGIDByLocation(pos));
        }
        let mine = instantiate(this.mine01Fab); // 也使用矿石的逻辑
        let ms = mine.getComponent(Mine) as Mine;
        ms.kind = GameConfig.blockKinds.ITEM;
        ms.initialHP = chapConfig.itemHP;
        ms.subKind = subKind;
        mine.setPosition(pos);
        mine.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 1;
        mine.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
        this.node.addChild(mine);
        this.blocks.push(mine);

        return [mine, ms];
    }

    getChapConfigByGID(gid: Vec3): [any, number] {
        let chapConfig = null;
        let chapIndex = -1;
        for (let i = 0; i < Chapters01.length; i++) {
            if (gid.y >= Chapters01[i]['levels'][0] && gid.y <= Chapters01[i]['levels'][1]) {
                chapIndex = i;
                chapConfig = Chapters01[i];
                break;
            }
        }
        return [chapConfig, chapIndex];
    }

    // 先创建矿石，再获取
    public getBlockAtGID(gid: Vec3): [Node, number] {
        let block = null;
        let idx = -1;
        for (let i = 0; i < this.blocks.length; i++) {
            // 检查节点是否存在且有效
            if (this.blocks[i] && this.blocks[i].position) {
                if (MapUtils.getGIDByLocation(this.blocks[i].position).equals(gid)) {
                    idx = i;
                    block = this.blocks[i];
                    break;
                }
            }
        }
        return [block, idx];
    }

    getGlowingTileAtGID(gid: Vec3): [Node, number] {
        let block = null;
        let idx = -1;
        for (let i = 0; i < this.glowingTiles.length; i++) {
            // 检查节点是否存在且有效
            if (this.glowingTiles[i] && this.glowingTiles[i].position) {
                if (MapUtils.getGIDByLocation(this.glowingTiles[i].position).equals(gid)) {
                    idx = i;
                    block = this.glowingTiles[i];
                    break;
                }
            }
        }
        return [block, idx];
    }

    // 这个专门用来拿石头的，不从blocks中获取，提升性能
    getRockAtGID(gid: Vec3): [Node, number] {
        let rock = null;
        let idx = -1;
        for (let i = 0; i < this.trapRocks.length; i++) {
            // 检查节点是否存在且有效
            if (this.trapRocks[i] && this.trapRocks[i].position) {
                if (MapUtils.getGIDByLocation(this.trapRocks[i].position).equals(gid)) {
                    idx = i;
                    rock = this.trapRocks[i];
                    break;
                }
            }
        }
        return [rock, idx];
    }

    markMineExposedAndRemoveItem(gid: Vec3) {
        let [b, idx] = this.getBlockAtGID(gid);
        if (idx > -1) {
            // 因为道具需要破坏才能出来，所以直接暂时删除
            if (b.getComponent(Mine).kind == GameConfig.blockKinds.ITEM) {
                b.destroy();
                this.blocks.splice(idx, 1);
            } else {
                // 矿石的话暴露出来
                b.getComponent(Mine).state = GameConfig.mineState.EXPOSED;
            }
        }

        // 放光矿石也需要删除掉
        let [glowing, idx2] = this.getGlowingTileAtGID(gid);
        if (idx2 > -1) {
            glowing.destroy();
            this.glowingTiles.splice(idx2, 1);
        }
    }

    public getScarAtGID(gid: Vec3): [Node, number] {
        let scar = null;
        let idx = -1;
        for (let i = 0; i < this.scars.length; i++) {
            // 检查节点是否存在且有效
            if (this.scars[i] && this.scars[i].position) {
                if (MapUtils.getGIDByLocation(this.scars[i].position).equals(gid)) {
                    idx = i;
                    scar = this.scars[i];
                    break;
                }
            }
        }
        return [scar, idx];
    }

    public removeScarAtGID(gid: Vec3) {
        for (let i = 0; i < this.scars.length; i++) {
            if (MapUtils.getGIDByLocation(this.scars[i].position).equals(gid)) {
                this.scars[i].destroy();
                this.scars.splice(i, 1);
                return;
            }
        }
    }

    // 检查指定位置是否是隐藏房间入口
    public isHiddenRoomEntrance(gid: Vec3): boolean {
        const chapConfig = GameData.currentLevelConfig;
        if (!chapConfig.hiddenRooms || !chapConfig.hiddenRooms.entranceGID) {
            return false;
        }
        const entranceGID = chapConfig.hiddenRooms.entranceGID;
        return gid.x === entranceGID[0] && gid.y === entranceGID[1];
    }

    // 检查玩家是否在隐藏房间入口位置
    public isPlayerOnHiddenRoomEntrance(playerPos: Vec3): boolean {
        const playerGID = MapUtils.getGIDByLocation(playerPos);
        // 检查该位置是否有隐藏房间入口道具
        for (let i = 0; i < this.blocks.length; i++) {
            let block = this.blocks[i];
            let blockGID = MapUtils.getGIDByLocation(block.position);
            if (blockGID.equals(playerGID)) {
                let mine = block.getComponent(Mine);
                if (
                    mine &&
                    mine.kind === GameConfig.blockKinds.ITEM &&
                    mine.subKind === GameConfig.blockKinds.ITEM_HIDDEN_ROOM_ENTRANCE
                ) {
                    return true;
                }
            }
        }
        return false;
    }

    // 是否在矿区内部，排除两侧的墙
    public isBetweenWall(gid: Vec3): boolean {
        if (!MapUtils.isGIDValid(gid, this.mapSize)) return false;
        // 隐藏房间的边界检查
        if (gid.x <= 1 || gid.x >= this.mapSize.width - 2) return false;
        return true;
    }

    // 如果dir = "no"，表示直接获取传入坐标的tile类型
    // posType: pos | gid
    public getNearTileType(sourcePos: Vec3, dir: string, posType: string): [number, Vec3] {
        let targetGID: Vec3;
        if (dir == 'no') {
            if (posType == 'pos') {
                targetGID = MapUtils.getGIDByLocation(sourcePos);
            } else {
                targetGID = sourcePos;
            }
        } else {
            if (posType == 'gid') {
                sourcePos = MapUtils.getLocationByGID(sourcePos);
            }
            targetGID = MapUtils.getNearGIDByLocation(sourcePos, dir, this.mapSize);
        }

        if (targetGID) {
            // 检查玩家是否在洞穴内，如果是则限制向下挖掘
            let playerGID = MapUtils.getGIDByLocation(sourcePos);
            if (this.isInHiddenRoomCave(playerGID.x, playerGID.y) && dir === 'down') {
                // 在洞穴内不允许向下挖掘，返回障碍物类型
                return [GameConfig.tileKinds.Obstacle, targetGID];
            }

            // 检查目标位置是否在洞穴内
            if (this.isInHiddenRoomCave(targetGID.x, targetGID.y)) {
                // 洞穴内部是空的
                return [GameConfig.tileKinds.Empty, targetGID];
            }

            for (let i = 0; i < this.exitGIDs.length; i++) {
                if (this.exitGIDs[i].equals(targetGID)) {
                    return [GameConfig.tileKinds.Exit, targetGID];
                }
            }

            let id = this.existInMapBlocks(targetGID.x, targetGID.y);
            // 边界检查
            if (
                targetGID.x < 1 ||
                targetGID.x >= this.mapSize.width - 1 ||
                targetGID.y < 1 ||
                targetGID.y >= this.mapSize.height - 1
            ) {
                return [GameConfig.tileKinds.Edge, null];
            } else {
                // 没有tile
                if (id == 0) {
                    // 空的情况下要看上面是否有障碍物
                    let [_, idx] = this.getRockAtGID(targetGID);
                    if (idx > -1) {
                        return [GameConfig.tileKinds.Obstacle, targetGID];
                    } else {
                        return [GameConfig.tileKinds.Empty, targetGID];
                    }
                } else {
                    return [GameConfig.tileKinds.Dirt, targetGID];
                }
            }
        } else {
            return [GameConfig.tileKinds.Edge, null];
        }
    }

    // this.groundLayer.getTileGIDAt(targetGID.x, targetGID.y); 代替这个方法返回的是0，表示没有tile
    public existInMapBlocks(GIDX: number, GIDY: number): number {
        for (let i = 0; i < this.mapBlocks.length; i++) {
            let b = this.mapBlocks[i];
            // 检查节点是否存在且有效
            if (b && b.position) {
                let gid = MapUtils.getGIDByLocation(b.position);
                if (gid.x == GIDX && gid.y == GIDY) {
                    return 1;
                }
            }
        }
        return 0;
    }

    gidInPlayerRange(gid: Vec3): boolean {
        let playerGID = MapUtils.getGIDByLocation(this.player?.position);
        if (
            gid.y < playerGID.y - GameConfig.maxUpdateLevelsRange ||
            gid.y > playerGID.y + GameConfig.maxUpdateLevelsRange
        ) {
            return false;
        }
        return true;
    }

    public spawnRockFallAtGID(gid: Vec3) {
        if (!this.gidInPlayerRange(gid)) return;
        for (let i = 0; i < math.randomRangeInt(3, 6); i++) {
            let pos = MapUtils.getLocationByGID(gid);
            let s = instantiate(this.rockFallFab);
            pos.y += math.randomRange(-20, 20);
            pos.x += math.randomRange(-30, 30);
            s.setPosition(pos);
            s.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 1;
            s.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
            this.node.addChild(s);
        }
    }

    public spawnRockFall2AtGID(gid: Vec3) {
        if (!this.gidInPlayerRange(gid)) return;
        let pos = MapUtils.getLocationByGID(gid);
        let s = instantiate(this.rockFall2Fab);
        s.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 1;
        s.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
        s.setPosition(pos);
        this.node.addChild(s);
    }

    public spawnSmokeAtGID(gid: Vec3) {
        if (!this.gidInPlayerRange(gid)) return;
        let s = instantiate(this.smokeFab);
        let pos = MapUtils.getLocationByGID(gid);
        pos.y += math.randomRange(20, 40);
        pos.x += math.randomRange(-20, 20);
        s.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 1;
        s.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
        s.setPosition(pos);
        this.node.addChild(s);
    }

    public spawnSawAtPos(pos: Vec3, level: number) {
        let gid = MapUtils.getGIDByLocation(pos);
        // left
        let saw = instantiate(this.sawFab);
        saw.setPosition(pos);
        saw.getComponent(Saw).flyingDir = 1;
        saw.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 1;
        saw.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
        this.node.addChild(saw);
        // right
        let saw2 = instantiate(this.sawFab);
        saw2.setPosition(pos);
        saw2.getComponent(Saw).flyingDir = -1;
        saw2.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 1;
        saw2.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
        this.node.addChild(saw2);

        if (level == 3) {
            let pos = MapUtils.getLocationByGID(new Vec3(gid.x, gid.y - 1, 0));
            let saw = instantiate(this.sawFab);
            saw.setPosition(pos);
            saw.getComponent(Saw).flyingDir = 1;
            saw.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 1;
            saw.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
            this.node.addChild(saw);
            let pos2 = MapUtils.getLocationByGID(new Vec3(gid.x, gid.y - 1, 0));
            let saw2 = instantiate(this.sawFab);
            saw2.setPosition(pos2);
            saw2.getComponent(Saw).flyingDir = -1;
            saw2.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 1;
            saw2.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
            this.node.addChild(saw2);

            let pos3 = MapUtils.getLocationByGID(new Vec3(gid.x, gid.y + 1, 0));
            let saw3 = instantiate(this.sawFab);
            saw3.setPosition(pos3);
            saw3.getComponent(Saw).flyingDir = 1;
            saw3.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 1;
            saw3.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
            this.node.addChild(saw3);
            let pos4 = MapUtils.getLocationByGID(new Vec3(gid.x, gid.y + 1, 0));
            let saw4 = instantiate(this.sawFab);
            saw4.setPosition(pos4);
            saw4.getComponent(Saw).flyingDir = -1;
            this.node.addChild(saw4);
        } else if (level == 5) {
            let pos = MapUtils.getLocationByGID(new Vec3(gid.x, gid.y - 1, 0));
            let saw = instantiate(this.sawFab);
            saw.setPosition(pos);
            saw.getComponent(Saw).flyingDir = 1;
            saw.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 1;
            saw.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
            this.node.addChild(saw);
            let pos2 = MapUtils.getLocationByGID(new Vec3(gid.x, gid.y - 1, 0));
            let saw2 = instantiate(this.sawFab);
            saw2.setPosition(pos2);
            saw2.getComponent(Saw).flyingDir = -1;
            saw2.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 1;
            saw2.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
            this.node.addChild(saw2);

            let pos3 = MapUtils.getLocationByGID(new Vec3(gid.x, gid.y + 1, 0));
            let saw3 = instantiate(this.sawFab);
            saw3.setPosition(pos3);
            saw3.getComponent(Saw).flyingDir = 1;
            saw3.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 1;
            saw3.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
            this.node.addChild(saw3);
            let pos4 = MapUtils.getLocationByGID(new Vec3(gid.x, gid.y + 1, 0));
            let saw4 = instantiate(this.sawFab);
            saw4.setPosition(pos4);
            saw4.getComponent(Saw).flyingDir = -1;
            saw4.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 1;
            saw4.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
            this.node.addChild(saw4);

            let pos5 = MapUtils.getLocationByGID(new Vec3(gid.x, gid.y - 2, 0));
            let saw5 = instantiate(this.sawFab);
            saw5.setPosition(pos5);
            saw5.getComponent(Saw).flyingDir = 1;
            saw5.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 1;
            saw5.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
            this.node.addChild(saw5);
            let pos6 = MapUtils.getLocationByGID(new Vec3(gid.x, gid.y - 2, 0));
            let saw6 = instantiate(this.sawFab);
            saw6.setPosition(pos6);
            saw6.getComponent(Saw).flyingDir = -1;
            saw6.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 1;
            saw6.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
            this.node.addChild(saw6);

            let pos7 = MapUtils.getLocationByGID(new Vec3(gid.x, gid.y + 2, 0));
            let saw7 = instantiate(this.sawFab);
            saw7.setPosition(pos7);
            saw7.getComponent(Saw).flyingDir = 1;
            saw7.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 1;
            saw7.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
            this.node.addChild(saw7);
            let pos8 = MapUtils.getLocationByGID(new Vec3(gid.x, gid.y + 2, 0));
            let saw8 = instantiate(this.sawFab);
            saw8.setPosition(pos8);
            saw8.getComponent(Saw).flyingDir = -1;
            saw8.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 1;
            saw8.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
            this.node.addChild(saw8);
        }
    }

    public spawnRoundBladeAtPos(pos: Vec3, dur: number) {
        let pos2 = MapUtils.getGIDByLocation(pos);
        pos2 = MapUtils.getLocationByGID(pos2);
        let blade = instantiate(this.roundBladeFab);
        blade.setPosition(pos2);
        blade.getComponent(RoundBlade).dur = dur;
        blade.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 1;
        blade.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
        this.node.addChild(blade);
    }

    public spawnTwinBladeAtPos(pos: Vec3, damage: number, dur: number) {
        let pos2 = MapUtils.getGIDByLocation(pos);
        pos2 = MapUtils.getLocationByGID(pos2);
        let blade = instantiate(this.twinBladeFab);
        blade.setPosition(pos2);
        blade.getComponent(TwinBlade).damage = damage;
        blade.getComponent(TwinBlade).dur = dur;
        blade.getComponent(UITransform).priority = GameConfig.UIPriority.allUI - 1;
        blade.setSiblingIndex(GameConfig.UIPriority.allUI - 1);
        this.node.addChild(blade);
    }

    public spawnFeetSmokeAtGID(gid: Vec3) {
        let s = instantiate(this.smokeFab);
        s.getComponent(Smoke).kind = 'walk';
        let pos = MapUtils.getLocationByGID(gid);
        pos.y -= 30;
        pos.x += math.randomRange(-2, 2);
        s.setPosition(pos);
        this.node.addChild(s);
    }

    // 物体移动结束后判断是否接触地面，接触的话产生烟雾
    public checkAndSpawnLandedSmoke(pos: Vec3) {
        let gid = MapUtils.getGIDByLocation(pos);
        if (!this.gidInPlayerRange(gid)) return;

        let [tt, downGID] = this.getNearTileType(pos, 'down', 'pos');
        if (tt != GameConfig.tileKinds.Empty) {
            this.spawnSmokeAtGID(downGID);
        }
    }

    // 关卡底部生成无法破坏的石头
    spawnUnbreakableRockAtGID(gid: Vec3): Node {
        let pos = MapUtils.getLocationByGID(gid);
        let trap = instantiate(this.trap01Fab); // 也使用矿石的逻辑
        let ms = trap.getComponent(Mine) as Mine;
        ms.kind = GameConfig.blockKinds.TRAP_ROCK;
        ms.canBeBroken = false;
        ms.initialHP = 9999;
        trap.getComponent(UITransform).priority = GameConfig.UIPriority.player;
        this.scheduleOnce(() => {
            trap.setSiblingIndex(GameConfig.UIPriority.player);
        }, 0);
        trap.setPosition(pos);
        this.node.addChild(trap);
        this.blocks.push(trap);
        this.trapRocks.push(trap);

        return trap;
    }

    // 石头落下，下方的道具和矿石会被压坏
    destroyBlocksAt(pos: Vec3) {
        let a = MapUtils.getGIDByLocation(pos);
        for (let i = 0; i < this.blocks.length; i++) {
            let bs = this.blocks[i].getComponent(Mine);
            if (
                bs.kind != GameConfig.blockKinds.TRAP_ROCK &&
                bs.state == GameConfig.mineState.EXPOSED
            ) {
                let b = MapUtils.getGIDByLocation(this.blocks[i].position);
                if (a.equals(b)) {
                    this.blocks[i].destroy();
                    this.spawnSmokeAtGID(a);
                    this.spawnSmokeAtGID(a);
                    this.spawnSmokeAtGID(a);
                    this.blocks.splice(i, 1);
                    i++;
                }
            }
        }
    }

    // 坐标转换方法已移动到 MapUtils 工具类中

    public convertChildPositionToUI(child: Node): Vec3 {
        let pos = child.parent.getComponent(UITransform).convertToWorldSpaceAR(child.position);
        pos = this.rootNode.getComponent(UITransform).convertToNodeSpaceAR(pos);
        return pos.subtract(this.mainCamera.position);
    }

    public convertUINodePositionToMap(child: Node): Vec3 {
        let pos = child.parent.getComponent(UITransform).convertToWorldSpaceAR(child.position);
        pos = this.rootNode.getComponent(UITransform).convertToNodeSpaceAR(pos);
        return pos.subtract(this.uiCamera.position);
    }

    public startShakeCamera() {
        if (this.mainCameraState != GameConfig.cameraStates.FOLLOWING) {
            return;
        }
        this.mainCameraState = GameConfig.cameraStates.START_SHAKING;
    }

    doShakeCamera(shakeTimes: number) {
        tween(this.mainCamera)
            .by(
                0.01,
                {
                    position: new Vec3(math.randomRange(-8, 5), math.randomRange(-8, 5), 0),
                },
                {
                    onComplete: () => {
                        if (shakeTimes <= 0) {
                            this.mainCameraState = GameConfig.cameraStates.FOLLOWING;
                            return;
                        }
                        shakeTimes--;
                        this.doShakeCamera(shakeTimes);
                    },
                }
            )
            .start();
    }

    /**
     * 在玩家附近创建出口
     * 出口不放在地图节点中，而是放在根节点中
     */
    private createExitNearPlayer() {
        // Exit现在在Canvas(rootNode)中，不在TiledMap中
        const exit = this.rootNode.getChildByName('Exit');
        if (!exit) {
            console.error('在Canvas节点中找不到名为Exit的子节点');
            // 尝试创建一个简单的替代物，确保游戏不会卡住
            this.tryCreateBackupExit();
            return;
        }

        // 确保添加了ExitDoor组件
        let exitDoorComponent = exit.getComponent(ExitDoor);
        if (!exitDoorComponent) {
            exitDoorComponent = exit.addComponent(ExitDoor);
        }

        // 设置玩家节点引用给ExitDoor组件
        exitDoorComponent.playerNode = this.player;

        try {
            // 计算出口的GID位置
            let exitGID = new Vec3(
                this.hiddenRoomCaveConfig.startX, // 洞穴入口处
                this.hiddenRoomCaveConfig.startY, // 洞穴顶部
                0
            );

            // 先获取基础位置
            let exitPos = MapUtils.getLocationByGID(exitGID);

            let tiledMapWorldPos = new Vec3();
            this.node.getWorldPosition(tiledMapWorldPos);

            // 将TiledMap的世界坐标转换为Canvas的本地坐标
            let tiledMapPosInCanvas = new Vec3();
            this.rootNode.inverseTransformPoint(tiledMapPosInCanvas, tiledMapWorldPos);

            // 简化坐标计算，不考虑缩放
            exitPos.x = tiledMapPosInCanvas.x + exitPos.x;
            exitPos.y = tiledMapPosInCanvas.y + exitPos.y;

            // 微调位置：Y轴下降3个瓦片，X轴右移2个瓦片避免被墙遮挡
            exitPos.y -= 3 * 64; // Y轴下降3个瓦片
            exitPos.y -= 30; // 微调
            exitPos.x += 2 * 64; // X轴右移2个瓦片

            exit.setPosition(exitPos);
        } catch (error) {
            console.error('创建出口时出错:', error);
        }
    }

    /**
     * 如果无法找到出口节点，创建一个应急的替代品
     */
    private tryCreateBackupExit() {
        try {
            // 创建一个简单的出口节点
            const backupExit = new Node('Exit');
            this.rootNode.addChild(backupExit);

            // 计算玩家位置附近的合适坐标
            const playerPos = this.player.position.clone();
            playerPos.x -= 200; // 放在玩家左侧
            backupExit.setPosition(playerPos);

            // 添加必要的组件
            const exitDoorComponent = backupExit.addComponent(ExitDoor);

            // 设置玩家节点引用
            exitDoorComponent.playerNode = this.player;
        } catch (error) {
            console.error('创建备用出口失败:', error);
        }
    }
    /**
     * 获取当前章节隐藏房间roomItems中coin的总数量
     */
    public getHiddenRoomCoinCount(): number {
        if (
            !this.currentChapterConfig ||
            !this.currentChapterConfig.hiddenRooms ||
            !this.currentChapterConfig.hiddenRooms.roomItems
        ) {
            return 0;
        }
        const roomItems = this.currentChapterConfig.hiddenRooms.roomItems;
        let total = 0;
        for (const item of roomItems) {
            if (item.type === 'coin') {
                total += item.amount ?? 1; // 没有amount字段时默认1
            }
        }
        return total;
    }

    update(deltaTime: number) {
        this.timePassed += deltaTime;
        if (!this.player) return;
        let ps = this.player?.getComponent(Player) as Player;
        let playerGID = MapUtils.getGIDByLocation(this.player?.position);

        if (this.mainCameraState == GameConfig.cameraStates.FOLLOWING) {
            let currentCameraPos = this.mainCamera.position.clone();

            let tempWorldPos = new Vec3();
            this.player.getWorldPosition(tempWorldPos);

            // 然后将世界坐标转换为相机父节点(Canvas)的本地坐标
            let targetPos = new Vec3();
            let cameraParent = this.mainCamera.parent;
            cameraParent.inverseTransformPoint(targetPos, tempWorldPos);

            let targetCameraX = targetPos.x;
            let targetCameraY = targetPos.y;

            let targetCameraPos = new Vec3(targetCameraX, targetCameraY, 0);

            // 5. 使用插值实现平滑跟随
            let followSpeed = 5.0; // 相机跟随速度
            let lerpFactor = Math.min(followSpeed * deltaTime, 1.0);
            let newCameraPos = currentCameraPos.lerp(targetCameraPos, lerpFactor);

            // 6. 应用新的相机位置
            this.mainCamera.setPosition(newCameraPos);

            // 7. 背景跟随相机
            if (this.bgScript) {
                this.bgScript.node.setPosition(this.mainCamera.position);
            }
        } else if (this.mainCameraState == GameConfig.cameraStates.START_SHAKING) {
            this.mainCameraState = GameConfig.cameraStates.SHAKING;
            this.doShakeCamera(math.randomRangeInt(4, 6));
        }

        if (ps.cannotControl()) return;

        //********************
        // 矿石自动往下落
        for (let i = 0; i < this.blocks.length; i++) {
            let b = this.blocks[i] as Node;
            // 检查节点是否存在且有效
            if (!b || !b.position) {
                continue;
            }
            let bs = b.getComponent(Mine) as Mine;
            // 检查 Mine 组件是否存在
            if (!bs) {
                continue;
            }
            let mGID = MapUtils.getGIDByLocation(b.position);
            // 不能破坏的石头用于固定平台，不会下落
            if (bs.kind == GameConfig.blockKinds.TRAP_ROCK && !bs.canBeBroken) {
                continue;
            }
            // 超过这个区间的不参与计算，节省性能
            if (!this.gidInPlayerRange(mGID)) {
                continue;
            }

            // 玩家碰撞矿石，道具，收集
            if (
                b.name == GameConfig.blockKindNames.MINE &&
                bs.state == GameConfig.mineState.EXPOSED
            ) {
                if (playerGID.equals(mGID)) {
                    // 如果是隐藏房间入口，完全跳过拾取处理
                    if (
                        bs.kind == GameConfig.blockKinds.ITEM &&
                        bs.subKind == GameConfig.blockKinds.ITEM_HIDDEN_ROOM_ENTRANCE
                    ) {
                        // 隐藏房间入口不进行任何拾取处理，保持在原位
                        // 不调用pickupItem，不播放动画，只是跳过
                        continue;
                    } else {
                        this.blocks.splice(i, 1);
                        i++;
                        if (bs.kind == GameConfig.blockKinds.ITEM) {
                            if (bs.subKind == GameConfig.blockKinds.ITEM_HIDDEN_MONEY) {
                                this.player
                                    ?.getComponent(Item)
                                    .hiddenRoomPickupItem(b, bs, this.getHiddenRoomCoinCount());
                            } else {
                                this.player?.getComponent(Item).hiddenRoomPickupItem(b, bs);
                            }

                            if (bs.subKind == GameConfig.blockKinds.ITEM_HIDDEN_MONEY) {
                                b.destroy();
                            } else if (bs.subKind == GameConfig.blockKinds.ITEM_KEY) {
                                b.destroy();
                            } else if (bs.subKind == GameConfig.blockKinds.ITEM_DIAMOND) {
                                b.destroy();
                            } else if (bs.subKind == GameConfig.blockKinds.ITEM_HEART) {
                                b.destroy();
                            } else if (bs.subKind == GameConfig.blockKinds.ITEM_LAMP) {
                                b.destroy();
                            } else {
                                tween(b)
                                    .by(
                                        0.3,
                                        {
                                            position: new Vec3(0, this.tileSize.height, 0),
                                        },
                                        {
                                            onComplete: () => {
                                                b.destroy();
                                            },
                                        }
                                    )
                                    .start();
                            }
                        } else {
                            // 矿石
                            this.player?.getComponent(Item).pickupMine(b, bs);
                        }
                    }
                }
                continue;
            }
            // 陷阱伤害
            if (
                b.name == GameConfig.blockKindNames.TRAP &&
                bs.state == GameConfig.mineState.EXPOSED
            ) {
                if (playerGID.equals(mGID)) {
                    let [chapConfig, _] = this.getChapConfigByGID(mGID);
                    ps.beDamaged(
                        chapConfig.damages[bs.kind],
                        false,
                        chapConfig.traps[bs.kind] == GameConfig.blockKinds.TRAP_ROCK
                            ? 'rock'
                            : 'spike'
                    );
                }
            }
        }
    }

    /**
     * 创建玩家出现在隐藏房间的动画
     */
    private createAppearInHiddenRoomAnimation() {
        // 实例化 hiddenRoomMask prefab
        const maskNode = instantiate(this.hiddenRoomMaskFab);

        // 添加到Canvas
        const canvas = director.getScene().getChildByPath('Canvas');
        canvas.addChild(maskNode);

        // 获取HiddenRoomMask组件
        const hiddenRoomMask = maskNode.getComponent(HiddenRoomMask);
        if (!hiddenRoomMask) {
            maskNode.destroy();
            return;
        }

        // 计算玩家位置在UI坐标系中的位置
        const playerWorldPos = this.convertChildPositionToUI(this.player);

        // 开始隐藏房间出现动画
        hiddenRoomMask.startAppearAnimation(playerWorldPos, () => {});
    }

    /**
     * 处理退出隐藏房间事件
     */
    private handleExitHiddenRoom(playerNode: Node) {
        // 如果已经在退出过程中，防止重复触发
        if (this.isExitingRoom) {
            return;
        }

        // 设置退出状态
        this.isExitingRoom = true;

        // 创建退出隐藏房间的动画
        this.createExitHiddenRoomAnimation(playerNode);
    }

    /**
     * 创建退出隐藏房间的动画
     */
    private createExitHiddenRoomAnimation(playerNode: Node) {
        // 实例化 hiddenRoomMask prefab
        const maskNode = instantiate(this.hiddenRoomMaskFab);

        // 添加到Canvas
        const canvas = director.getScene().getChildByPath('Canvas');
        canvas.addChild(maskNode);

        // 获取HiddenRoomMask组件
        const hiddenRoomMask = maskNode.getComponent(HiddenRoomMask);
        if (!hiddenRoomMask) {
            maskNode.destroy();
            return;
        }

        // 获取出口位置作为动画原点
        let exitWorldPos: Vec3;

        // 方法1：尝试找到出口门节点
        const exit = this.rootNode.getChildByName('Exit');
        if (exit) {
            // 使用出口门的位置
            exitWorldPos = this.convertChildPositionToUI(exit);
            console.log(`[HIDDEN_MAP] 📍 使用出口门UI位置: (${exitWorldPos.x}, ${exitWorldPos.y})`);
        } else {
            // 方法2：使用玩家当前位置作为备选
            exitWorldPos = this.convertChildPositionToUI(playerNode);
            console.log(`[HIDDEN_MAP] 📍 使用玩家UI位置作为出口位置: (${exitWorldPos.x}, ${exitWorldPos.y})`);
        }

        // 开始退出隐藏房间动画
        hiddenRoomMask.startEnterAnimation(exitWorldPos, () => {
            director.loadScene('game');
        });
    }

    /**
     * 处理宝箱打开事件
     */
    private onTreasureOpened(data: any) {
        // 在宝箱被打开时，使用当前章节配置的入口GID保存状态
        // 使用 this.currentChapterConfig 而不是 GameData.currentLevelConfig 确保配置一致性
        if (
            this.currentChapterConfig &&
            this.currentChapterConfig.hiddenRooms &&
            this.currentChapterConfig.hiddenRooms.entranceGID
        ) {
            const entranceGID = this.currentChapterConfig.hiddenRooms.entranceGID;
            const entranceGIDObj = { x: entranceGID[0], y: entranceGID[1] };

            // 检查宝箱是否已经开启过（在重新进入房间时）
            const wasAlreadyOpened = HiddenRoomStateManager.isTreasureOpened(entranceGIDObj);

            // 只在第一次打开时保存宝箱开启状态，避免多个奖励重复保存
            if (!this.treasureStateAlreadySaved && !wasAlreadyOpened) {
                HiddenRoomStateManager.saveTreasureOpened(entranceGIDObj);
                this.treasureStateAlreadySaved = true;
            }
        } else {
            return;
        }

        if (!this.uiNode) {
            return;
        }

        // 根据奖励类型播放不同的飞行动画
        this.playRewardFlyAnimation(data);
    }

    /**
     * 播放奖励飞行动画
     * @param rewardData { type, amount, position, node }
     */
    private playRewardFlyAnimation(rewardData: any) {
        const { type, amount, position, node } = rewardData;
        let startPos: Vec3;
        // 优先用node（如宝箱节点）获取准确UI坐标
        if (node && node instanceof Node) {
            startPos = this.convertChildPositionToUI(node);
        } else if (position) {
            // 兼容旧逻辑
            startPos = this.convertWorldPositionToUI(position);
        } else {
            startPos = new Vec3(0, 0, 0);
        }
        this.createAndAnimateRewardIcon(type, startPos, amount || 1);
    }

    /**
     * 创建并播放奖励图标动画
     * @param rewardType 奖励类型
     * @param startPos 起始UI坐标
     * @param amount 奖励数量
     */
    private createAndAnimateRewardIcon(rewardType: string, startPos: Vec3, amount: number = 1) {
        if (!this.commonAssets) {
            return;
        }

        // 获取GameManager组件来使用backpackS预制体
        const gameManagerNode = director.getScene().getChildByPath('Canvas/GameManager');
        if (!gameManagerNode) {
            return;
        }
        const gameManager = gameManagerNode.getComponent('GameManager') as any;
        if (!gameManager || !gameManager.backpackS) {
            return;
        }

        // 使用和Item.ts相同的方式创建奖励图标
        const rewardIcon = instantiate(gameManager.backpackS);
        const sprite = rewardIcon.getChildByName('Sprite').getComponent(Sprite);

        let targetUIPath = '';
        switch (rewardType) {
            case 'diamond':
                sprite.spriteFrame = this.commonAssets.itemDiamondFrame;
                targetUIPath = 'Diamond';
                break;
            case 'coin':
            case 'money':
                sprite.spriteFrame = this.coin || this.commonAssets.itemCoinFrame;
                targetUIPath = 'Coin';
                break;
            case 'key':
                sprite.spriteFrame = this.commonAssets.itemKeyFrame;
                targetUIPath = 'Keys';
                break;
            default:
                sprite.spriteFrame = this.commonAssets.itemDiamondFrame;
                targetUIPath = 'Diamond';
                break;
        }

        rewardIcon.setPosition(startPos);
        this.uiNode.addChild(rewardIcon);

        // 获取目标节点的本地坐标
        const targetNode = this.uiNode.getChildByPath(targetUIPath);
        if (!targetNode) {
            rewardIcon.destroy();
            return;
        }

        const targetPos = targetNode.getPosition();

        // 使用和Item.ts相同的动画时长和效果
        tween(rewardIcon)
            .parallel(
                tween().to(1, { position: new Vec3(targetPos.x, targetPos.y, 0) }),
                tween().to(1, { scale: new Vec3(0.5, 0.5, 1) })
            )
            .call(() => {
                rewardIcon.destroy();
                EventBus.emit('playSound', 'pickupChest');
                this.updatePlayerReward(rewardType, amount);
            })
            .start();
    }

    /**
     * 将世界坐标转换为UI坐标
     */
    private convertWorldPositionToUI(worldPos: Vec3): Vec3 {
        if (!this.uiNode) {
            return worldPos.clone();
        }

        // 这里需要根据实际的坐标系统进行转换
        // 简化处理，直接使用世界坐标
        return worldPos.clone();
    }

    /**
     * 更新玩家奖励数据
     */
    private updatePlayerReward(rewardType: string, amount: number) {
        switch (rewardType) {
            case 'diamond':
                UserData.currentLevelDiamondCount += amount;
                break;
            case 'coin':
            case 'money':
                UserData.money += amount;
                break;
            case 'key':
                UserData.currentLevelKeyCount += amount;
                break;
            default:
                console.warn(`HiddenMap: 未知的奖励类型: ${rewardType}`);
                break;
        }

        // 发送UI更新事件
        EventBus.emit('updateUI');
    }
}
